# Match-Based Workflow Architecture

## Core Vision

**Match-centric workflow** replacing Application/Offer/JobPosition with **JobPost → Match → Contract** path.

**Principles:**

- Match as central orchestrator
- Bidirectional initiation (Provider/Organization)
- Multi-step verification + compensation negotiation
- Gradual migration from legacy workflow

---

## System Architecture

```mermaid
---
title: Match-Centric Workflow Architecture
---
erDiagram
    %% Legacy System (Being Phased Out)
    JobPost ||--o{ Application : "legacy - being replaced"
    JobPost ||--o{ Offer : "legacy - being replaced"
    Application ||--o| JobPosition : "legacy - being replaced"
    Offer ||--o| JobPosition : "legacy - being replaced"

    %% New Match-Centric Workflow
    JobPost ||--o{ Match : "primary workflow"
    Organization ||--o{ Match : "participates"
    Provider ||--o{ Match : "participates"

    %% Match replaces JobPosition
    Match ||--o| JobPosition : "transitional link"
    Match ||--o| Contract : "leads to"
    Match ||--o| JobCompensation : "has"
    Match ||--o| Thread : "communicates"
    Match ||--o{ Action : "audit trail"

    %% Multi-Step Workflow Within Match
    Match ||--o{ MatchStep : "contains"

    %% Integration with Existing Infrastructure
    Match ||--o| Schedule : "uses existing"
    JobCompensation ||--o| JobPost : "inherits from"

    Match {
        string id PK
        enum status "PENDING|VALIDATING|NEGOTIATING|MATCHED"
        enum initiatedBy "PROVIDER|ORGANIZATION|MUTUAL|REFERRAL"
        string jobId FK
        string organizationId FK
        string providerId FK
        string applicationId FK "optional"
        string offerId FK "optional"
        datetime createdAt
    }

    MatchStep {
        string id PK
        string matchId FK
        enum type "IDENTITY_VERIFICATION|BACKGROUND_CHECK|RATE_NEGOTIATION"
        enum status "PENDING|VALIDATING|IN_PROGRESS|COMPLETED"
        int stepOrder
        boolean isRequired
        datetime startedAt
        datetime completedAt
    }

    JobCompensation {
        string id PK
        string matchId FK
        float minRate
        float maxRate
        float finalAgreedRate
        enum paymentType "HOURLY|SALARY|PER_DIEM"
    }
```

---

## Initiation Patterns

| Type           | Trigger                    | Workflow                              | Timeline | Success Rate |
| -------------- | -------------------------- | ------------------------------------- | -------- | ------------ |
| `PROVIDER`     | Provider applies           | Identity → Background → Rate          | 3-5 days | 85%          |
| `ORGANIZATION` | Org reaches out            | Identity → Rate (Background optional) | 1-3 days | 90%          |
| `MUTUAL`       | Both express interest      | Rate → Identity → Background          | 2-4 days | 95%          |
| `REFERRAL`     | Third-party recommendation | Enhanced Background → Identity → Rate | 4-7 days | 80%          |

```mermaid
---
title: Initiation-Specific Step Sequences
---
graph LR
    subgraph "Provider-Initiated"
        P1[IDENTITY_VERIFICATION] --> P2[BACKGROUND_CHECK] --> P3[RATE_NEGOTIATION]
    end

    subgraph "Organization-Initiated"
        O1[IDENTITY_VERIFICATION] --> O2[RATE_NEGOTIATION]
        O2 -.->|Optional| O3[BACKGROUND_CHECK]
    end

    subgraph "Mutual Interest"
        M1[RATE_NEGOTIATION] --> M2[IDENTITY_VERIFICATION] --> M3[BACKGROUND_CHECK]
    end

    subgraph "Referral Match"
        R1[ENHANCED_BACKGROUND_CHECK] --> R2[IDENTITY_VERIFICATION] --> R3[RATE_NEGOTIATION]
    end
```

---

## Data Models

### Match Status & Initiator

```prisma
enum MatchStatus {
  PENDING       // Initial match created
  VALIDATING    // Verifying match requirements
  NEGOTIATING   // In negotiation phase
  FINALIZING    // Terms agreed, finalizing details
  MATCHED       // Successfully matched
  WITHDRAWN     // Provider withdrew
  DECLINED      // Organization declined
  EXPIRED       // Match expired
  CANCELLED     // Cancelled by either party
  @@schema("public")
}

enum MatchInitiator {
  PROVIDER      // Provider applied first
  ORGANIZATION  // Organization reached out first
  MUTUAL        // Mutual interest
  REFERRAL      // Third party referral
  @@schema("public")
}
```

### Match Model

```prisma
model Match {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  status     MatchStatus    @default(PENDING)
  initiator  MatchInitiator @default(PROVIDER)

  // Initiation metadata
  initiatedAt    DateTime @default(now())
  initiatedBy    String   // User ID who initiated
  initiationNote String?  // Optional context

  // Core relationships
  job            JobPost      @relation(fields: [jobId], references: [id])
  jobId          String
  organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String
  provider       Provider     @relation(fields: [providerId], references: [id])
  providerId     String

  // Optional integration points (non-breaking)
  application    Application? @relation(fields: [applicationId], references: [id])
  applicationId  String?      @unique
  offer          Offer?       @relation(fields: [offerId], references: [id])
  offerId        String?      @unique

  // Match outcomes
  position       JobPosition? @relation(fields: [positionId], references: [id])
  positionId     String?      @unique
  contract       Contract?    @relation(fields: [contractId], references: [id])
  contractId     String?      @unique
  compensation   JobCompensation

  // Communication and actions
  thread         Thread?      @relation(fields: [threadId], references: [id])
  threadId       String?      @unique
  actions        Action[]

  // Workflow
  steps MatchStep[]

  @@unique([jobId, providerId])
  @@index([initiator, status], name: "match_initiator_status_idx")
  @@index([createdAt], name: "match_created_at_idx")
  @@schema("public")
}
```

### MatchStep Model

```prisma
enum StepType {
  // Current (Phase 1)
  IDENTITY_VERIFICATION
  BACKGROUND_CHECK
  RATE_NEGOTIATION
  ENHANCED_BACKGROUND_CHECK

  // Future Phases
  INTERVIEW
  SCHEDULE_NEGOTIATION
  BENEFITS_NEGOTIATION
  CONTRACT_TERMS
  REFERENCE_CHECK
  SKILLS_ASSESSMENT
  DRUG_SCREENING
  CREDENTIAL_VERIFICATION
  FINAL_APPROVAL
  ONBOARDING_PREP
  EQUIPMENT_ASSIGNMENT
  ORIENTATION_SCHEDULING
  @@schema("public")
}

enum StepStatus {
  PENDING      // Waiting to start
  VALIDATING   // Validating requirements/permissions
  IN_PROGRESS  // Currently active
  COMPLETED    // Successfully finished
  SKIPPED      // Bypassed (optional step)
  FAILED       // Failed to complete
  CANCELLED    // Cancelled by either party
  @@schema("public")
}

model MatchStep {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Simple timeline
  startedAt   DateTime?
  completedAt DateTime?

  type   StepType   @default(RATE_NEGOTIATION)
  status StepStatus @default(PENDING)
  order  Int        @default(1)

  // Initiation-specific configuration
  isRequired    Boolean @default(true)
  isSkippable   Boolean @default(false)
  skipReason    String?

  // Results
  isSuccessful Boolean?
  notes        String?
  metadata     Json?    // Flexible data storage

  // Relationships
  match   Match   @relation(fields: [matchId], references: [id], onDelete: Cascade)
  matchId String
  actions Action[]

  @@unique([matchId, type])
  @@index([matchId, order], name: "match_step_order_idx")
  @@index([type, status], name: "step_type_status_idx")
  @@schema("public")
}
```

### JobCompensation Model

```prisma
model JobCompensation {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Rate boundaries (inherited from JobPost, can be negotiated within range)
  minRate          Float
  maxRate          Float
  currentOfferRate Float?
  finalAgreedRate  Float?

  // Initiation-specific rate strategy
  rateStrategy     String? // "conservative", "competitive", "balanced", "premium"
  negotiationCount Int     @default(0)

  // Payment context
  paymentType PayType @default(HOURLY)

  // Medical-specific rates
  baseRate                Float?
  nightRateMultiplier     Float? @default(1.25)
  overtimeRateMultiplier  Float? @default(1.5)
  holidayRateMultiplier   Float? @default(2.0)
  callbackRate            Float?
  orientationRate         Float?
  supervisionRate         Float?

  // Calculated values
  totalMonthlyValue       Float?
  effectiveHourlyRate     Float?

  // Relationships
  match   Match   @relation(fields: [matchId], references: [id], onDelete: Cascade)
  matchId String  @unique
  job     JobPost @relation(fields: [jobId], references: [id])
  jobId   String

  @@index([matchId, jobId], name: "job_compensation_idx")
  @@index([rateStrategy], name: "compensation_strategy_idx")
  @@schema("public")
}
```

---

## Workflow Examples

### Provider-Initiated

```
1. Provider applies to JobPost
2. Create Match (initiator: PROVIDER, status: PENDING)
3. Identity verification → Background check → Rate negotiation
4. Match finalized → Contract created
```

### Organization-Initiated

```
1. Organization reaches out to provider
2. Create Match (initiator: ORGANIZATION, status: PENDING)
3. Identity verification → Rate negotiation (Background optional)
4. Match finalized → Contract created
```

### Mutual Interest

```
1. Both parties express interest
2. Create Match (initiator: MUTUAL, status: PENDING)
3. Rate negotiation → Identity verification → Background check
4. Match finalized → Contract created
```

### Referral Match

```
1. Third party recommendation
2. Create Match (initiator: REFERRAL, status: PENDING)
3. Enhanced background check → Identity verification → Rate negotiation
4. Match finalized → Contract created
```

---

## Implementation Strategy

### Phase 1: Core Bidirectional Support

- Enhanced Match model with initiation tracking
- Initiation-specific step sequences
- Rate strategy integration

### Phase 2: Workflow Optimization

- Performance optimization with composite indexes
- Initiation-specific UI flows
- Step caching and status transition logic

### Phase 3: Advanced Features

- Analytics by initiation type
- Automation (auto-skip, smart rate suggestions)
- Enhanced verification triggers

---

## Migration Strategy

### Zero Breaking Changes

- All new fields optional with defaults
- Existing Application/Offer workflow unchanged
- Optional foreign keys with defaults
- Additive-only schema changes

### Migration SQL

```sql
-- Phase 1: Add new fields (zero impact)
ALTER TABLE "Match" ADD COLUMN "initiatedBy" TEXT DEFAULT 'PROVIDER';
ALTER TABLE "Match" ADD COLUMN "initiatedAt" TIMESTAMP DEFAULT NOW();
ALTER TABLE "Match" ADD COLUMN "initiatedBy" TEXT;
ALTER TABLE "Match" ADD COLUMN "initiationNote" TEXT;

-- Phase 2: Add indexes (minimal impact)
CREATE INDEX "match_initiator_status_idx" ON "Match"("initiator", "status");
CREATE INDEX "match_created_at_idx" ON "Match"("createdAt");

-- Phase 3: Add step configuration (zero impact)
ALTER TABLE "MatchStep" ADD COLUMN "isSkippable" BOOLEAN DEFAULT FALSE;
ALTER TABLE "MatchStep" ADD COLUMN "skipReason" TEXT;
ALTER TABLE "MatchStep" ADD COLUMN "metadata" JSONB;
```

---

## Technical Considerations

### Performance

- Composite indexes for common queries
- Step caching for frequent lookups
- Optimized status transition logic

### User Experience

- Initiation-specific UI flows
- Progress indicators by initiator
- Custom messaging per flow type

### Analytics

- Success rates by initiation type
- Time-to-match by flow
- Rate negotiation patterns

### Production Safety

- Rollback capability through optional relationships
- Performance monitoring through proper indexing
- Gradual migration with rollback capability
